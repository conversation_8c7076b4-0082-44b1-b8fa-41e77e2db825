<template>
  <div id="recipe-step-detail">
    <a-modal
      :open="visible"
      :footer="null"
      :closable="true"
      @cancel="handleClose"
      :width="600"
      :mask-closable="false"
      class="ingredient-detail-modal"
      width="100%"
      wrap-class-name="full-modal"
    >
      <!-- <RecipeStepFlow></RecipeStepFlow> -->
      <VueFlow :nodes="nodes" :edges="edges"></VueFlow>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import RecipeStepFlow from '@/components/recipe/RecipeStepFlow.vue'
import { ref } from 'vue'
import '@vue-flow/core/dist/style.css'

/* this contains the default theme, these are optional styles */
import '@vue-flow/core/dist/theme-default.css'
interface Props {
  visible: boolean
  mode: 'edit' | 'view' // 编辑模式或查看模式
  recipeStepList: []
  title?: string
  editable?: boolean // 是否允许编辑（即使在edit模式下也可以设为false来只读）
}

// Props定义
const props = withDefaults(defineProps<Props>(), {
  visible: false,
  mode: 'view',
  recipeStepList: () => [],
  title: '',
  editable: true,
})

// Emits定义
const emit = defineEmits<{
  close: []
  save: [recipeStepList: []]
}>()
const saving = ref(false)
const handleClose = () => {
  emit('close')
}
const handleSave = async () => {}

const nodes = ref([
  // an input node, specified by using `type: 'input'`
  {
    id: '1',
    type: 'input',
    position: { x: 250, y: 5 },
    // all nodes can have a data object containing any data you want to pass to the node
    // a label can property can be used for default nodes
    data: { label: 'Node 1' },
  },

  // default node, you can omit `type: 'default'` as it's the fallback type
  {
    id: '2',
    position: { x: 100, y: 100 },
    data: { label: 'Node 2' },
  },

  // An output node, specified by using `type: 'output'`
  {
    id: '3',
    type: 'output',
    position: { x: 400, y: 200 },
    data: { label: 'Node 3' },
  },

  // this is a custom node
  // we set it by using a custom type name we choose, in this example `special`
  // the name can be freely chosen, there are no restrictions as long as it's a string
  {
    id: '4',
    position: { x: 400, y: 200 },
    data: {
      label: 'Node 4',
      hello: 'world',
    },
  },
])

// these are our edges
const edges = ref([
  // default bezier edge
  // consists of an edge id, source node id and target node id
  {
    id: 'e1->2',
    source: '1',
    target: '2',
  },

  // set `animated: true` to create an animated edge path
  {
    id: 'e2->3',
    source: '2',
    target: '3',
    animated: true,
  },

  // a custom edge, specified by using a custom type name
  // we choose `type: 'special'` for this example
  {
    id: 'e3->4',
    source: '3',
    target: '4',

    // all edges can have a data object containing any data you want to pass to the edge
    data: {
      hello: 'world',
    },
  },
])
</script>

<style lang="less" scoped>
// recipe-step-detail {
//   min-height: 300px;
// }
.full-modal {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
  }
  .ant-modal-body {
    flex: 1;
  }
}
</style>
